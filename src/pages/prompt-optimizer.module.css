/**
 * CSS for the Prompt Optimizer page
 */

/* Page section styles */
.pageSection {
  padding: 4rem 0;
  overflow: hidden;
}

/* Hero section styles */
.hero {
  padding: 5rem 0;
  position: relative;
}

.heroBadge {
  display: inline-block;
  background-color: rgba(74, 111, 255, 0.1);
  color: #4A6FFF;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  margin-bottom: 1rem;
  letter-spacing: 0.5px;
}

.heroContent {
  max-width: 600px;
  padding-right: 2rem;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.btnPrimary {
  background-color: #4A6FFF !important;
  color: white !important;
  font-weight: 600;
  padding: 0.8rem 1.5rem !important;
  border-radius: 8px !important;
  transition: all 0.2s ease;
  border: none !important;
}

.btnPrimary:hover {
  background-color: #3A5FEF !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 111, 255, 0.2);
}

.btnSecondary {
  background-color: white !important;
  color: #4A6FFF !important;
  font-weight: 600;
  padding: 0.8rem 1.5rem !important;
  border-radius: 8px !important;
  border: 1px solid #4A6FFF !important;
  transition: all 0.2s ease;
}

.btnSecondary:hover {
  background-color: #f0f7ff !important;
  transform: translateY(-2px);
}

.heroStats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
}

.heroStat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  align-self: center;
  flex: 1;
}

.heroStatNumber {
  font-size: 1.8rem;
  font-weight: 700;
  color: #4A6FFF;
}

.heroStatLabel {
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.3rem;
}

.heroImageContainer {
  position: relative;
}

.heroImageWrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

.heroImageWrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.heroImage {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 12px;
}

.heroImageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 1.5rem 1rem 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.heroImageWrapper:hover .heroImageOverlay {
  opacity: 1;
}

.heroImageOverlayText {
  color: white;
  font-size: 0.9rem;
  text-align: center;
  display: block;
}

.heroRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  /* gap: 3rem; */
  flex-wrap: wrap;
}

@media (max-width: 996px) {
  .heroRow {
    flex-direction: column;
    /* gap: 2rem; */
  }
}

@media (max-width: 768px) {
  .heroRow {
    flex-direction: column;
    /* gap: 2rem; */
  }
}

/* Responsive styles for hero section */
@media (max-width: 996px) {
  .heroContent {
    max-width: 100%;
    padding-right: 0;
    margin-bottom: 2rem;
  }

  .heroStats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 2rem 0;
  }

  .heroContent,
  .heroImageContainer {
    width: 100%;
    max-width: 100%;
  }

  .heroContent {
    margin-bottom: 2rem;
  }

  .heroButtons {
    flex-direction: column;
    gap: 1rem;
  }

  .heroStat {
    min-width: 0;
    flex: 1 1 100%;
    margin-bottom: 1rem;
  }

  .heroStats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .heroImageContainer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .heroImageWrapper {
    width: 100%;
    margin: 0 auto;
  }
}

.pricingCards {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 3rem;
  width: 100%;
}

.pricingCard {
  flex: 1;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.pricingCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}
.pricingCardTitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.pricingFeatures {
  font-size: 1.2rem;
  color: #333;
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricingNote {
  font-size: 1rem;
  color: #666;
  margin-top: 1rem;
  font-style: italic;
}

/* 针对featureSection和pricingSection等容器的移动端适配 */
@media (max-width: 768px) {

  .featureSection,
  .pricingContainer {
    padding: 1rem 0.5rem;
  }

  .pricingCards {
    flex-direction: column;
    gap: 2rem;
  }

  .pricingFeatures {
    font-size: 1rem;
  }
 

  .featureGrid {
    flex-direction: column !important;
    gap: 2rem;
  }

  .featureContent,
  .featureImageWrapper {
    width: 100%;
    max-width: 100%;
  }

  .pricingContainer {
    flex-direction: column;
    gap: 2rem;
    justify-content: flex-start;
  }

  .pricingCard {
    width: 100%;
    max-width: 100%;
  }
}

/* Section styling */
.sectionHeading {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.sectionDescription {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.6;
}

/* Workflow steps */
.workflowSteps {
  display: flex;
  justify-content: space-between;
  margin: 3rem 0;
  gap: 2rem;
}

.workflowStep {
  flex: 1;
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.workflowStep:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stepNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #4A6FFF;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
}

.stepTitle {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.stepDescription {
  color: #666;
  line-height: 1.5;
}

/* Feature grid */
.featureGrid {
  display: flex;
  align-items: center;
  gap: 3rem;
  margin: 4rem 0;
  padding: 2rem;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.06);
}

.featureContent {
  flex: 1;
}

.featureBadge {
  display: inline-block;
  background-color: rgba(74, 111, 255, 0.1);
  color: #4A6FFF;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.3rem 0.7rem;
  border-radius: 20px;
  margin-bottom: 1rem;
  letter-spacing: 0.5px;
}

.featureTitle {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #333;
}

.featureDescription {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureList li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 0.8rem;
}

.featureIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  background-color: #4A6FFF;
  color: white;
  font-size: 0.9rem;
  border-radius: 50%;
  margin-top: 0.2rem;
}

.featureImageWrapper {
  flex: 1;
}

.featureImageContainer {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  cursor: pointer;
}

.featureImageContainer:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.featureImage {
  display: block;
  width: 100%;
  height: auto;
  border-radius: 12px;
}

.featureImageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 1.5rem 1rem 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featureImageContainer:hover .featureImageOverlay {
  opacity: 1;
}

.featureImageOverlayText {
  color: white;
  font-size: 0.9rem;
  text-align: center;
  display: block;
}

/* Feature Section */
.featureSection {
  padding: 5rem 0;
  position: relative;
}

/* CTA Container */
.ctaContainer {
  text-align: center;
  padding: 3rem;
  margin: 4rem 0 2rem;
  background: linear-gradient(135deg, #f0f7ff 0%, rgba(81, 111, 242, 0.1) 100%);
  border-radius: 16px;
}

.ctaTitle {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #333;
}

.ctaDescription {
  font-size: 1.2rem;
  color: #555;
  max-width: 700px;
  margin: 0 auto 2rem;
}

.ctaButton {
  padding: 1rem 2.5rem !important;
  font-size: 1.1rem !important;
}

/* Responsive styles for features section */
@media (max-width: 996px) {
  .workflowSteps {
    flex-direction: column;
    gap: 1.5rem;
  }

  .featureGrid {
    flex-direction: column !important;
    padding: 1.5rem;
  }

  .featureImageWrapper {
    width: 100%;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .featureTitle {
    font-size: 1.5rem;
  }

  .ctaTitle {
    font-size: 1.8rem;
  }
}