import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';

import styles from './index.module.css';
import promptOptimizerStyles from './prompt-optimizer.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import TestimonialsSection from '../components/TestimonialsSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import CTASection from '../components/CTASection';
import PromptOptimizerStructuredData from '../components/PromptOptimizerStructuredData';
import ComparisonSection from '../components/ComparisonSection';
import VideoSection from '../components/VideoSection';
import BenefitsSection from '../components/BenefitsSection';

function PromptOptimizerHeader({ setShowImageSrc, toApp }) {
  return (
    <section id="hero" className={clsx(promptOptimizerStyles.hero, promptOptimizerStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #f0f7ff 0%, rgb(81, 111, 242) 100%)'
    }}>
      <div className="container">
        <div
          className={promptOptimizerStyles.heroRow}
        >
          <div className={promptOptimizerStyles.heroContent} style={{ flex: 1, minWidth: 0 }}>
            <div className={promptOptimizerStyles.heroBadge}>
              <Translate id="prompt_optimizer.hero.badge">
                NEW BROWSER EXTENSION
              </Translate>
            </div>
            <Heading as="h1">
              <Translate id="prompt_optimizer.hero.title">
                Transform Your AI Conversations with the Prompt Optimizer
              </Translate>
            </Heading>
            <p className={promptOptimizerStyles.heroSubtitle}>
              <Translate id="prompt_optimizer.hero.subtitle">
                Get better answers from ChatGPT, Claude, Gemini, and more with one-click prompt optimization
              </Translate>
            </p>

            <div className={promptOptimizerStyles.heroButtons}>
              <Link
                className={clsx('button', promptOptimizerStyles.btnPrimary)}
                to="#"
                onClick={() => toApp()}
              >
                <Translate id="prompt_optimizer.hero.trial">Download Extension for FREE</Translate>
              </Link>
              <Link
                className={clsx('button', promptOptimizerStyles.btnSecondary)}
                to="#how-it-works"
              >
                <Translate id="prompt_optimizer.hero.learn_more">See How It Works</Translate>
              </Link>
            </div>

            <div className={promptOptimizerStyles.heroStats}>
              <div className={promptOptimizerStyles.heroStat}>
                <span className={promptOptimizerStyles.heroStatNumber}>30+</span>
                <span className={promptOptimizerStyles.heroStatLabel}>
                  <Translate id="prompt_optimizer.hero.stat1">Free Optimizations</Translate>
                </span>
              </div>
              <div className={promptOptimizerStyles.heroStat}>
                <span className={promptOptimizerStyles.heroStatNumber}>5</span>
                <span className={promptOptimizerStyles.heroStatLabel}>
                  <Translate id="prompt_optimizer.hero.stat2">Supported AI Platforms</Translate>
                </span>
              </div>
              <div className={promptOptimizerStyles.heroStat}>
                <span className={promptOptimizerStyles.heroStatNumber}>4.7★</span>
                <span className={promptOptimizerStyles.heroStatLabel}>
                  <Translate id="prompt_optimizer.hero.stat3">User Rating</Translate>
                </span>
              </div>
            </div>
          </div>
          <div className={promptOptimizerStyles.heroImageContainer} style={{ flex: 1, minWidth: 0, display: 'flex', justifyContent: 'center' }}>
            <div className={promptOptimizerStyles.heroImageWrapper} >
              <img
                className={promptOptimizerStyles.heroImage}
                onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_hero.png")}
                id="prompt-optimizer-overview"
                alt="FunBlocks AI Prompt Optimizer Extension interface"
                src="/img/portfolio/fullsize/prompt_optimizer_hero.png"
              />
              <div className={promptOptimizerStyles.heroImageOverlay}>
                <span className={promptOptimizerStyles.heroImageOverlayText}>
                  <Translate id="prompt_optimizer.hero.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function HowItWorksSection({ setShowImageSrc, toApp }) {
  return (
    <section id="how-it-works" className={promptOptimizerStyles.featureSection}>
      <div className="container">
        <div className={promptOptimizerStyles.sectionHeading}>
          <Heading as="h2" className={promptOptimizerStyles.sectionTitle}>
            <Translate id="prompt_optimizer.how_it_works.title">How the Prompt Optimizer Works</Translate>
          </Heading>
          <p className={promptOptimizerStyles.sectionDescription}>
            <Translate id="prompt_optimizer.how_it_works.description">
              Our browser extension seamlessly integrates with leading AI platforms like ChatGPT, Claude, and Gemini, providing powerful prompt engineering tools right where you need them
            </Translate>
          </p>
        </div>

        <div className={promptOptimizerStyles.workflowSteps}>
          <div className={promptOptimizerStyles.workflowStep}>
            <div className={promptOptimizerStyles.stepNumber}>1</div>
            <Heading as="h3" className={promptOptimizerStyles.stepTitle}>
              <Translate id="prompt_optimizer.how_it_works.step1.title">Install the Extension</Translate>
            </Heading>
            <p className={promptOptimizerStyles.stepDescription}>
              <Translate id="prompt_optimizer.how_it_works.step1.description">
                Add the Prompt Optimizer to Chrome or Edge in just a few clicks. No complex setup required.
              </Translate>
            </p>
          </div>
          <div className={promptOptimizerStyles.workflowStep}>
            <div className={promptOptimizerStyles.stepNumber}>2</div>
            <Heading as="h3" className={promptOptimizerStyles.stepTitle}>
              <Translate id="prompt_optimizer.how_it_works.step2.title">Visit Your Favorite AI Chat</Translate>
            </Heading>
            <p className={promptOptimizerStyles.stepDescription}>
              <Translate id="prompt_optimizer.how_it_works.step2.description">
                Open ChatGPT, Claude, Gemini, or any supported AI platform and see our tools automatically appear.
              </Translate>
            </p>
          </div>
          <div className={promptOptimizerStyles.workflowStep}>
            <div className={promptOptimizerStyles.stepNumber}>3</div>
            <Heading as="h3" className={promptOptimizerStyles.stepTitle}>
              <Translate id="prompt_optimizer.how_it_works.step3.title">Optimize Your Prompts</Translate>
            </Heading>
            <p className={promptOptimizerStyles.stepDescription}>
              <Translate id="prompt_optimizer.how_it_works.step3.description">
                Use our one-click tools to transform basic questions into powerful, precise prompts that get better results.
              </Translate>
            </p>
          </div>
        </div>

        {/* <div className={promptOptimizerStyles.featureGrid} style={{ width: 1120, height: 700, borderRadius: 0, flexDirection: 'column', justifyContent: 'center' }}>
          <div className={promptOptimizerStyles.featureContent} style={{ flex: 0 }}>
            <div className={promptOptimizerStyles.featureBadge}>
              <Translate id="prompt_optimizer.how_it_works.feature1.badge">FEATURE HIGHLIGHT</Translate>
            </div>
            <Heading as="h1" className={promptOptimizerStyles.featureTitle} style={{ fontSize: 32 }}>
              Refine Question & Optimize Prompt
            </Heading>
          
            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  The extension adds a Prompt Optimizer widget below the input box in AI chat applications
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Our AI analyzes your prompts and suggests improvements based on proven prompt engineering techniques
                </div>
              </li>

            </ul>

           
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper} style={{ maxWidth: 800, flex: 0 }}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_hero.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_hero.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>



        <div className={promptOptimizerStyles.featureGrid} style={{ width: 1120, height: 700, borderRadius: 0, flexDirection: 'row', padding: 80 }}>
          <div className={promptOptimizerStyles.featureContent} style={{ flex: 3 }}>
            <div className={promptOptimizerStyles.featureBadge}>
              <Translate id="prompt_optimizer.how_it_works.feature1.badge">FEATURE HIGHLIGHT</Translate>
            </div>
            <Heading as="h1" className={promptOptimizerStyles.featureTitle} style={{ fontSize: 32 }}>
              Unlimited Exploration
            </Heading>
            <div style={{ marginBottom: 20 }}>
              Discover new dimensions of knowledge with our unique exploration tools

            </div>

            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Uncover related questions & topics beyond your initial query and AI response
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Gain deeper insights and broader perspectives through AI guided discovery
                </div>
              </li>

            </ul>

            
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper} style={{ maxWidth: 800, flex: 5 }}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_related.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_related.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>



         <div className={promptOptimizerStyles.featureGrid} style={{ width: 1120, height: 700, borderRadius: 0, flexDirection: 'row', padding: 80 }}>
          <div className={promptOptimizerStyles.featureImageWrapper} style={{ maxWidth: 800, flex: 4 }}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_form.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_form.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>

          <div className={promptOptimizerStyles.featureContent} style={{ flex: 3 }}>
            <div className={promptOptimizerStyles.featureBadge} style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              SMART FORM
            </div>
            <Heading as="h1" className={promptOptimizerStyles.featureTitle} style={{ fontSize: 32 }}>
              Dynamic Form for Missing Information
            </Heading>
          

            <ul className={promptOptimizerStyles.featureList}>
              <li>
               <div className={promptOptimizerStyles.featureIcon}>📝</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point1">
                    Instantly detects missing context or requirements in your prompt
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>➡️</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point2">
                    Presents a simple form to fill in the gaps—no guesswork needed
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✅</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point3">
                    Your optimized prompt is generated with all the required details for better AI answers
                  </Translate>
                </div>
              </li>
            </ul>

            
          </div>
          
        </div>



         <div className={promptOptimizerStyles.featureGrid} style={{ width: 1120, height: 700, borderRadius: 0, flexDirection: 'row', padding: 80 }}>
          <div className={promptOptimizerStyles.featureImageWrapper} style={{ maxWidth: 800, flex: 4 }}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_related_topics.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_related_topics.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>

          <div className={promptOptimizerStyles.featureContent} style={{ flex: 3 }}>
            <div className={promptOptimizerStyles.featureBadge} style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              UNLIMITED EXPLORATION
            </div>
            <Heading as="h1" className={promptOptimizerStyles.featureTitle} style={{ fontSize: 32 }}>
              Unlocking Hidden Insights
            </Heading>
            
            <div style={{ marginBottom: 20 }}>
              Dive deeper into your inquiry with AI-driven suggestions.
            </div>

            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Venture beyond your initial query and AI responses
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Explore deeper insights with follow-up questions
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Discover broader perspectives through related topics
                </div>
              </li>

            </ul>
            
          </div>
          
        </div>

        <div className={promptOptimizerStyles.featureGrid} style={{ width: 1120, height: 700, borderRadius: 0, flexDirection: 'row', padding: 80 }}>
          <div className={promptOptimizerStyles.featureImageWrapper} style={{ maxWidth: 800, flex: 4 }}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_hero.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_refined_questions.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>

          <div className={promptOptimizerStyles.featureContent} style={{ flex: 3 }}>
            <div className={promptOptimizerStyles.featureBadge} style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              REFINED QUESTIONS
            </div>
            <Heading as="h1" className={promptOptimizerStyles.featureTitle} style={{ fontSize: 32 }}>
              Well-Asked Is Half-Solved
            </Heading>
            
            

            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  AI helps you rephrase your question to be clearer and more effective
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Refined prompts lead to more accurate and relevant AI responses
                </div>
              </li>

              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Improve outcomes across writing, coding, research, and more
                </div>
              </li>

              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  Learn from the optimized prompts to enhance your own questioning skills
                </div>
              </li>

            </ul>
            
          </div>
          
        </div> */}

        <div className={promptOptimizerStyles.featureGrid}  style={{ flexDirection: 'row-reverse' }}>
          <div className={promptOptimizerStyles.featureContent}>
            <div className={promptOptimizerStyles.featureBadge}>
              <Translate id="prompt_optimizer.how_it_works.feature1.badge">FEATURE HIGHLIGHT</Translate>
            </div>
            <Heading as="h3" className={promptOptimizerStyles.featureTitle}>
              <Translate id="prompt_optimizer.how_it_works.feature1.title">Smart Question & Instruction Optimization</Translate>
            </Heading>
            <p className={promptOptimizerStyles.featureDescription}>
              <Translate id="prompt_optimizer.how_it_works.feature1.description">
                The extension adds a Prompt Optimizer widget below the input box in AI chat applications. Our AI analyzes your prompts and suggests improvements based on proven prompt engineering techniques.
              </Translate>
            </p>

            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature1.point1">
                    Choose "Optimize Question" to generate 5 more accurate, specific, or different perspectives on your question
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature1.point2">
                    Select "Optimize Instruction" to clarify your prompt based on your intent and core needs
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature1.point3">
                    If your instruction needs more information, a dynamic form will appear to help you provide the necessary details
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature1.point4">
                    Replace your original prompt with the optimized version with a single click
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_hero.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization interface"
                src="/img/portfolio/fullsize/prompt_optimizer_hero.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className={promptOptimizerStyles.featureGrid}>
          <div className={promptOptimizerStyles.featureContent}>
            <div className={promptOptimizerStyles.featureBadge} style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              <Translate id="prompt_optimizer.how_it_works.feature5.badge">REFINED QUESTIONS</Translate>
            </div>
            <Heading as="h3" className={promptOptimizerStyles.featureTitle}>
              <Translate id="prompt_optimizer.how_it_works.feature5.title">Well-Asked Is Half-Solved</Translate>
            </Heading>
            <p className={promptOptimizerStyles.featureDescription}>
              <Translate id="prompt_optimizer.how_it_works.feature5.description">
                Better input means better output. Our tools guide you to write sharper prompts for clearer, more reliable AI results.
              </Translate>
            </p>
            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature5.point1">
                    AI helps you rephrase your question to be clearer and more effective
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature5.point2">
                    Refined prompts lead to more accurate and relevant AI responses
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature5.point3">
                    Improve outcomes across writing, coding, research, and more
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature5.point4">
                    Learn from the optimized prompts to enhance your own questioning skills
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_refined_questions.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization generated related topics and questions"
                src="/img/portfolio/fullsize/prompt_optimizer_refined_questions.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 新增动态表单功能介绍 */}
        <div className={promptOptimizerStyles.featureGrid}>
          <div className={promptOptimizerStyles.featureContent}>
            <div className={promptOptimizerStyles.featureBadge}  style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              <Translate id="prompt_optimizer.how_it_works.feature3.badge">SMART FORM</Translate>
            </div>
            <Heading as="h3" className={promptOptimizerStyles.featureTitle}>
              <Translate id="prompt_optimizer.how_it_works.feature3.title">Dynamic Form for Missing Information</Translate>
            </Heading>
            <p className={promptOptimizerStyles.featureDescription}>
              <Translate id="prompt_optimizer.how_it_works.feature3.description">
                If your instruction is missing key details, the Prompt Optimizer will automatically display a dynamic form. This form guides you to provide the necessary information, ensuring your prompt is clear and complete for the best AI results.
              </Translate>
            </p>
            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>📝</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point1">
                    Instantly detects missing context or requirements in your prompt
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>➡️</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point2">
                    Presents a simple form to fill in the gaps—no guesswork needed
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✅</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature3.point3">
                    Your optimized prompt is generated with all the required details for better AI answers
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_form.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization dynamic form interface"
                src="/img/portfolio/fullsize/prompt_optimizer_form.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className={promptOptimizerStyles.featureGrid} style={{ flexDirection: 'row-reverse' }}>
          <div className={promptOptimizerStyles.featureContent}>
            <div className={promptOptimizerStyles.featureBadge}>
              <Translate id="prompt_optimizer.how_it_works.feature2.badge">EXCLUSIVE FEATURE</Translate>
            </div>
            <Heading as="h3" className={promptOptimizerStyles.featureTitle}>
              <Translate id="prompt_optimizer.how_it_works.feature2.title">AI-Powered Topic Exploration</Translate>
            </Heading>
            <p className={promptOptimizerStyles.featureDescription}>
              <Translate id="prompt_optimizer.how_it_works.feature2.description">
                Unlock deeper insights and broader perspectives with our unique exploration tools that help you discover related questions and topics you might not have considered.
              </Translate>
            </p>

            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature2.point1">
                    Access "Related Questions" and "Related Topics" buttons in the toolbar of each message
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature2.point2">
                    Generate questions and topics based on the current message content using advanced AI analysis
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature2.point3">
                    Explore topics more deeply and broadly with AI-generated suggestions that expand your thinking
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature2.point4">
                    Continue your conversation with enhanced context and direction for more productive AI interactions
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_related.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-related"
                alt="FunBlocks AI Prompt Optimizer related questions and topics interface"
                src="/img/portfolio/fullsize/prompt_optimizer_related.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature2.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className={promptOptimizerStyles.featureGrid}>
          <div className={promptOptimizerStyles.featureContent}>
            <div className={promptOptimizerStyles.featureBadge}  style={{ backgroundColor: '#e0f2ea', color: 'green' }}>
              <Translate id="prompt_optimizer.how_it_works.feature4.badge">UNLIMITED EXPLORATION</Translate>
            </div>
            <Heading as="h3" className={promptOptimizerStyles.featureTitle}>
              <Translate id="prompt_optimizer.how_it_works.feature4.title">Generated Exploration Space</Translate>
            </Heading>
            <p className={promptOptimizerStyles.featureDescription}>
              <Translate id="prompt_optimizer.how_it_works.feature4.description">
                Our unique exploration tools that help you discover related questions and topics you might not have considered.
              </Translate>
            </p>
            <ul className={promptOptimizerStyles.featureList}>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature4.point1">
                    Explore deeper insights with follow-up questions
                  </Translate>
                </div>
              </li>
              <li>
                <div className={promptOptimizerStyles.featureIcon}>✓</div>
                <div>
                  <Translate id="prompt_optimizer.how_it_works.feature4.point2">
                    Discover broader perspectives through related topics
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={promptOptimizerStyles.featureImageWrapper}>
            <div className={promptOptimizerStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_related_topics.png")}>
              <img
                className={promptOptimizerStyles.featureImage}
                id="prompt-optimizer-question"
                alt="FunBlocks AI Prompt Optimizer question optimization generated related topics and questions"
                src="/img/portfolio/fullsize/prompt_optimizer_related_topics.png"
              />
              <div className={promptOptimizerStyles.featureImageOverlay}>
                <span className={promptOptimizerStyles.featureImageOverlayText}>
                  <Translate id="prompt_optimizer.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className={promptOptimizerStyles.ctaContainer}>
          <Heading as="h3" className={promptOptimizerStyles.ctaTitle}>
            <Translate id="prompt_optimizer.how_it_works.cta.title">Ready to transform your AI conversations?</Translate>
          </Heading>
          <p className={promptOptimizerStyles.ctaDescription}>
            <Translate id="prompt_optimizer.how_it_works.cta.description">
              Join thousands of users who are getting better results from AI with optimized prompts
            </Translate>
          </p>
          <Link
            className={clsx('button', promptOptimizerStyles.btnPrimary, promptOptimizerStyles.ctaButton)}
            to="#"
            onClick={() => toApp()}
          >
            <Translate id="prompt_optimizer.how_it_works.cta.button">Download Extension for FREE</Translate>
          </Link>
        </div>
      </div>
    </section>
  );
}

function WhyItMattersSection() {
  return (
    <section id="why-it-matters" className={styles.featureSection} style={{ backgroundColor: 'honeydew' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="prompt_optimizer.why_it_matters.title">Why Good Prompts Matter</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="prompt_optimizer.why_it_matters.description">
            In the age of generative AI, the ability to ask good questions is a crucial skill
          </Translate>
        </p>

        <div className={styles.benefitsContainer}>
          <div className={styles.benefitCard}>
            <div className={styles.cardTitle}>
              <div className={styles.benefitIcon}>🔑</div>
              <Heading as="h4">
                <Translate id="prompt_optimizer.why_it_matters.point1.title">The Key to Better Answers</Translate>
              </Heading>
            </div>
            <p>
              <Translate id="prompt_optimizer.why_it_matters.point1.description">
                AI models have vast knowledge and powerful capabilities, but they need clear, specific instructions to deliver their best results. Good prompts unlock their full potential.
              </Translate>
            </p>
          </div>

          <div className={styles.benefitCard}>
            <div className={styles.cardTitle}>
              <div className={styles.benefitIcon}>🧠</div>
              <Heading as="h4">
                <Translate id="prompt_optimizer.why_it_matters.point2.title">A Critical Human Skill</Translate>
              </Heading>
            </div>
            <p>
              <Translate id="prompt_optimizer.why_it_matters.point2.description">
                As AI becomes more integrated into our work and lives, the ability to ask good questions and provide clear instructions becomes an increasingly valuable human skill.
              </Translate>
            </p>
          </div>

          <div className={styles.benefitCard}>
            <div className={styles.cardTitle}>
              <div className={styles.benefitIcon}>🚀</div>
              <Heading as="h4">
                <Translate id="prompt_optimizer.why_it_matters.point3.title">Competitive Advantage</Translate>
              </Heading>
            </div>
            <p>
              <Translate id="prompt_optimizer.why_it_matters.point3.description">
                Those who can effectively communicate with AI will have a significant advantage in productivity, creativity, and problem-solving in the AI-powered future.
              </Translate>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

function PricingSection({ bg = '#fff' }) {
  return (
    <section id="pricing" className={styles.featureSection} style={{ backgroundColor: bg }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="prompt_optimizer.pricing.title">Try It For Free</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="prompt_optimizer.pricing.description">
            Get started with our generous free trial and flexible subscription options
          </Translate>
        </p>
        <div className={promptOptimizerStyles.pricingContainer} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '2rem' }}>
          <div className={promptOptimizerStyles.pricingCards}>
            <div className={promptOptimizerStyles.pricingCard}>
              <Heading as="h3" className={promptOptimizerStyles.pricingTitle}>
                <Translate id="prompt_optimizer.pricing.free_trial.title">Free Trial</Translate>
              </Heading>
              <ul className={promptOptimizerStyles.featureList}>
                <li>
                  <div className={promptOptimizerStyles.featureIcon}>✓</div>
                  <Translate id="prompt_optimizer.pricing.free_trial.feature1">30 free optimizations for new users</Translate>
                </li>
                <li>
                  <div className={promptOptimizerStyles.featureIcon}>✓</div>
                  <Translate id="prompt_optimizer.pricing.free_trial.feature2">10 free optimizations daily</Translate>
                </li>
                <li>
                  <div className={promptOptimizerStyles.featureIcon}>✓</div>
                  <Translate id="prompt_optimizer.pricing.free_trial.feature3">Access to all core features</Translate>
                </li>
              </ul>
              <p className={promptOptimizerStyles.pricingNote}>
                <Translate id="prompt_optimizer.pricing.free_trial.note">No credit card required</Translate>
              </p>
            </div>
            <div className={promptOptimizerStyles.pricingCard}>
              <Heading as="h3" className={styles.pricingTitle}>
                <Translate id="prompt_optimizer.pricing.subscription.title">FunBlocks AI Plans</Translate>
              </Heading>
              <ul className={promptOptimizerStyles.featureList}>
                <li>
                  <div className={promptOptimizerStyles.featureIcon} style={{ backgroundColor: 'limegreen' }}>✓</div>
                  <Translate id="prompt_optimizer.pricing.subscription.feature1">Included in all FunBlocks AI subscription plans</Translate>
                </li>
                <li>
                  <div className={promptOptimizerStyles.featureIcon} style={{ backgroundColor: 'limegreen' }}>✓</div>
                  <Translate id="prompt_optimizer.pricing.subscription.feature2">Choose the plan that fits your needs</Translate>
                </li>
                <li>
                  <div className={promptOptimizerStyles.featureIcon} style={{ backgroundColor: 'limegreen' }}>✓</div>
                  <Translate id="prompt_optimizer.pricing.subscription.feature3">Access to the entire FunBlocks AI ecosystem</Translate>
                </li>
              </ul>
              <p className={promptOptimizerStyles.pricingNote}>
                <Translate id="prompt_optimizer.pricing.subscription.note">See pricing page for details</Translate>
              </p>
            </div>
          </div>
          <Link
            className={clsx('button', promptOptimizerStyles.btnPrimary)}
            to="/pricing"
            style={{ marginTop: '1.5rem', minWidth: 200, textAlign: 'center' }}
          >
            <Translate id="prompt_optimizer.pricing.button">View Pricing Plans</Translate>
          </Link>
        </div>
      </div>
    </section>
  );
}

export default function PromptOptimizer() {
  useDocusaurusContext();
  const [showImageSrc, setShowImageSrc] = useState(null);

  function getDomain() {
    if (!window.location.hostname.includes('funblocks')) {
      return 'funblocks.net';
    }
    return window.location.hostname.replace('www.', '');
  }

  function openUrl(url) {
    let newTab = window.open();
    newTab.location.href = url;
  }

  function toApp() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
    const isEdge = userAgent.includes('edg');
    
    if (isChrome || isEdge) {
      let url = 'https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh';
      openUrl(url);
    } else {
      alert(translate({
        id: 'prompt_optimizer.browser_not_supported',
        message: 'This extension is only supported on Chrome and Edge browsers. Please use Chrome or the new version of Edge to install the extension.'
      }));
    }
  }

  const testimonials_avatars = ["👨‍💻", "👩‍💼", "👨‍🎓", "👩‍🏫", "👨‍💼", "👩‍🎓"];

  return (
    <Layout
      title={translate({
        id: 'prompt_optimizer.head.title',
        message: 'FunBlocks AI Prompt Optimizer Extension - Enhance AI Conversations with Better Prompts'
      })}
      description={translate({
        id: 'prompt_optimizer.head.description',
        message: 'Optimize your prompts for ChatGPT, Claude, Gemini, Perplexity, DeepSeek and more. Get better AI answers with improved questions and instructions. Generate related questions and explore topics more deeply with our browser extension.'
      })}
      keywords={translate({
        id: 'prompt_optimizer.head.keywords',
        message: 'AI prompt optimizer, ChatGPT prompts, Claude prompts, AI conversation enhancement, prompt engineering, AI assistant, browser extension, better AI answers, prompt optimization tool'
      })}
    >
      <PromptOptimizerStructuredData />

      <PromptOptimizerHeader setShowImageSrc={setShowImageSrc} toApp={toApp} />
      <main>
        <BenefitsSection
          page="prompt_optimizer"
          backgroundColor="honeydew"
          customBenefits={[
            {
              icon: '🎯',
              titleId: 'prompt_optimizer.benefits.benefit1.title',
              title: 'More Accurate Responses',
              descriptionId: 'prompt_optimizer.benefits.benefit1.description',
              description: 'Get precisely what you need from AI with optimized prompts that clearly communicate your intent and requirements.'
            },
            {
              icon: '⏱️',
              titleId: 'prompt_optimizer.benefits.benefit2.title',
              title: 'Save Time & Effort',
              descriptionId: 'prompt_optimizer.benefits.benefit2.description',
              description: 'Eliminate back-and-forth clarifications by starting with well-crafted prompts that address all necessary details.'
            },
            {
              icon: '💡',
              titleId: 'prompt_optimizer.benefits.benefit3.title',
              title: 'Improve Your Prompting Skills',
              descriptionId: 'prompt_optimizer.benefits.benefit3.description',
              description: 'Learn by example as you see how the extension transforms basic prompts into powerful instructions.'
            },
            {
              icon: '🔍',
              titleId: 'prompt_optimizer.benefits.benefit4.title',
              title: 'Deeper Exploration',
              descriptionId: 'prompt_optimizer.benefits.benefit4.description',
              description: 'Discover related questions and topics to explore subjects more thoroughly and from different perspectives.'
            },
            {
              icon: '🔌',
              titleId: 'prompt_optimizer.benefits.benefit5.title',
              title: 'Works With Your Favorite AI Tools',
              descriptionId: 'prompt_optimizer.benefits.benefit5.description',
              description: 'Compatible with ChatGPT, Claude, Gemini, Perplexity, DeepSeek and other popular AI chat applications.'
            },
            {
              icon: '🚀',
              titleId: 'prompt_optimizer.benefits.benefit6.title',
              title: 'One-Click Implementation',
              descriptionId: 'prompt_optimizer.benefits.benefit6.description',
              description: 'Seamlessly replace your original prompt with the optimized version with just a single click.'
            }
          ]}
        />
        <section id="less-is-more" className={styles.featureSection} style={{ backgroundColor: '#f8f9fa' }}>
          <div className="container">
            <div className={styles.sectionHeading}>
              <Heading as="h2" className={styles.sectionTitle}>
                <Translate id="prompt_optimizer.less_is_more.title">Get More from AI with Less Instructions</Translate>
              </Heading>
              <p className={styles.sectionDescription}>
                <Translate id="prompt_optimizer.less_is_more.description">
                  Sometimes less is more when it comes to AI prompts. Start with your goal and let AI do the heavy lifting.
                </Translate>
              </p>
            </div>

            <div className={styles.benefitsContainer}>
              <div className={styles.benefitCard}>
                <div className={styles.cardTitle}>
                  <div className={styles.benefitIcon}>🎯</div>
                  <Heading as="h4">
                    <Translate id="prompt_optimizer.less_is_more.point1.title">Focus on Your Goal</Translate>
                  </Heading>
                </div>
                <p>
                  <Translate id="prompt_optimizer.less_is_more.point1.description">
                    Instead of detailed instructions, simply state what you want to achieve. Let AI analyze and plan the best approach.
                  </Translate>
                </p>
              </div>

              <div className={styles.benefitCard}>
                <div className={styles.cardTitle}>
                  <div className={styles.benefitIcon}>🚀</div>
                  <Heading as="h4">
                    <Translate id="prompt_optimizer.less_is_more.point2.title">Break Free from Limitations</Translate>
                  </Heading>
                </div>
                <p>
                  <Translate id="prompt_optimizer.less_is_more.point2.description">
                    Detailed instructions can limit AI's creativity. By focusing on goals, you open doors to solutions you might not have considered.
                  </Translate>
                </p>
              </div>

              <div className={styles.benefitCard}>
                <div className={styles.cardTitle}>
                  <div className={styles.benefitIcon}>💡</div>
                  <Heading as="h4">
                    <Translate id="prompt_optimizer.less_is_more.point3.title">Let AI Do the Heavy Lifting</Translate>
                  </Heading>
                </div>
                <p>
                  <Translate id="prompt_optimizer.less_is_more.point3.description">
                    The Prompt Optimizer helps you start with simple goals, then uses AI to expand and enhance your prompts for better results.
                  </Translate>
                </p>
              </div>
            </div>

            <div className={styles.ctaContainer} style={{ marginTop: '2rem', textAlign: 'center' }}>
              <p className={styles.ctaDescription}>
                <Translate id="prompt_optimizer.less_is_more.cta">
                  Experience how less can truly be more with the Prompt Optimizer
                </Translate>
              </p>
              <Link
                className={clsx('button', promptOptimizerStyles.btnPrimary)}
                to="#"
                onClick={() => toApp()}
                style={{ marginTop: '1rem' }}
              >
                <Translate id="prompt_optimizer.less_is_more.button">Try It Now</Translate>
              </Link>
            </div>
          </div>
        </section>
        <HowItWorksSection setShowImageSrc={setShowImageSrc} toApp={toApp} />
        <WhyItMattersSection />

        <ComparisonSection
          page="prompt_optimizer"
          titleTranslateId="prompt_optimizer.comparison.title"
          descriptionTranslateId="prompt_optimizer.comparison.description"
          noteTranslateId="prompt_optimizer.comparison.note"
          competitors={{
            funblocks: {
              label: <Translate id="prompt_optimizer.comparison.funblocksHeader">FunBlocks AI Prompt Optimizer</Translate>,
              isHighlighted: true
            },
            chatgpt: {
              label: <Translate id="prompt_optimizer.comparison.chatgptHeader">Standard ChatGPT</Translate>,
              isHighlighted: false
            },
            promptEngineering: {
              label: <Translate id="prompt_optimizer.comparison.promptEngineeringHeader">Manual Prompt Engineering</Translate>,
              isHighlighted: false
            },
            otherExtensions: {
              label: <Translate id="prompt_optimizer.comparison.otherExtensionsHeader">Other AI Extensions</Translate>,
              isHighlighted: false
            }
          }}
          customData={[
            {
              feature: <Translate id="prompt_optimizer.comparison.feature1">One-Click Prompt Optimization</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: false,
              otherExtensions: 'Limited'
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature2">Dynamic Form for Missing Information</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: false,
              otherExtensions: false
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature3">Related Questions Generation</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: false,
              otherExtensions: 'Some'
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature4">Related Topics Exploration</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: false,
              otherExtensions: 'Rare'
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature5">Multi-Platform Support</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: true,
              otherExtensions: 'Limited'
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature6">Improves Prompting Skills</Translate>,
              funblocks: true,
              chatgpt: false,
              promptEngineering: true,
              otherExtensions: 'Some'
            },
            {
              feature: <Translate id="prompt_optimizer.comparison.feature7">Free Daily Usage</Translate>,
              funblocks: true,
              chatgpt: true,
              promptEngineering: true,
              otherExtensions: 'Varies'
            }
          ]}
        />

        {/* <VideoSection
          page="prompt_optimizer"
          videoId="tPjuWOjpJIs" // Placeholder - replace with actual video ID
          titleTranslateId="prompt_optimizer.video.title"
          descriptionTranslateId="prompt_optimizer.video.description"
          ctaTranslateId="prompt_optimizer.video.cta"
          customFeatures={[
            {
              icon: '🔍',
              title: <Translate id="prompt_optimizer.video.feature1.title">See It In Action</Translate>,
              description: <Translate id="prompt_optimizer.video.feature1.description">
                Watch how the extension seamlessly integrates with AI chat applications
              </Translate>
            },
            {
              icon: '💡',
              title: <Translate id="prompt_optimizer.video.feature2.title">Learn Best Practices</Translate>,
              description: <Translate id="prompt_optimizer.video.feature2.description">
                Discover effective prompt optimization techniques you can apply yourself
              </Translate>
            },
            {
              icon: '🚀',
              title: <Translate id="prompt_optimizer.video.feature3.title">Explore Use Cases</Translate>,
              description: <Translate id="prompt_optimizer.video.feature3.description">
                See real-world examples of how optimized prompts improve AI responses
              </Translate>
            }
          ]}
        /> */}

        <PricingSection bg='azure' />

        <TestimonialsSection avatars={testimonials_avatars} page={'prompt_optimizer'} />
        <CTASection 
          toApp={toApp} 
          page={'prompt_optimizer'} 
          customButtonText={<Translate id="prompt_optimizer.cta.button">Download Extension for FREE</Translate>}
        />
        <FAQSection
          page={'prompt_optimizer'}
          faqIds={[
            'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8', 'q9', 'q10', 'q11', 'q12'
          ]}
        />
      </main>
      <Footer />

      {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
      <GoogleAccountAnalytics page={'prompt_optimizer'} />
    </Layout>
  );
}
