/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

:root {
  --primary: #4A6FFF;
  --primary-dark: #3A5BDF;
  --secondary: #FF6B6B;
  --dark: #333333;
  --light: #FFFFFF;
  --gray: #F5F7FA;
  --text: #333333;
  --text-light: #555555;
}

.comparisonSection {
  padding: 6rem 0;
  background-color: azure;
}

.responsiveContainer {
  padding: 0 1.5rem;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.sectionDescription {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
  font-size: 1.3rem;
  color: var(--text-light);
}

.tableContainer {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  overflow-x: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS devices */
}

.comparisonTable {
  width: 100%;
  min-width: 650px; /* Ensures table doesn't get too narrow on mobile */
  margin: 0;
  border-collapse: collapse;
  background-color: white;
  border-radius: 2px;
  overflow: hidden;
  table-layout: fixed; /* Ensures equal column widths */
}

.comparisonTable th,
.comparisonTable td {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid #eaeaea;
  word-break: break-word; /* Prevents text from overflowing */
  vertical-align: middle; /* Vertically centers content */
}

.comparisonTable th {
  background-color: #f0f4ff;
  font-weight: 600;
  color: var(--text);
}

.featureHeader {
  text-align: left;
  width: 35%;
}

/* Set equal width for competitor columns */
.comparisonTable th:not(.featureHeader),
.comparisonTable td:not(.featureCell) {
  width: calc((100% - 35%) / var(--competitor-count, 4)); /* Dynamically calculate width based on number of competitors */
  font-size: 1.1rem; /* Slightly larger for better visibility */
}

.funblocksHeader {
  background-color: var(--primary) !important;
  color: white !important;
}

.featureCell {
  text-align: left;
  font-weight: 500;
}

.funblocksCell {
  background-color: rgba(74, 111, 255, 0.05);
  font-weight: 600;
}

/* .comparisonTable tr:last-child td {
  border-bottom: none;
} */

.comparisonNote {
  max-width: 700px;
  margin: 2rem auto 0;
  text-align: center;
  font-style: italic;
  color: var(--text-light);
}

/* Mobile indicator for horizontal scrolling */
.scrollIndicator {
  display: none;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-light);
  font-size: 0.9rem;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 992px) {
  .comparisonSection {
    padding: 4rem 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    padding: 0 1rem;
  }

  .comparisonNote {
    padding: 0 1rem;
  }
}

@media (max-width: 768px) {
  .scrollIndicator {
    display: block;
  }

  .comparisonTable th,
  .comparisonTable td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }

  .sectionTitle {
    font-size: 1.8rem;
    padding: 0 1rem;
  }

  .sectionDescription {
    font-size: 1rem;
  }

  .comparisonNote p {
    font-size: 0.9rem;
  }

  .responsiveContainer {
    padding: 0 1rem;
  }
}

@media (max-width: 576px) {
  .comparisonSection {
    padding: 3rem 0;
  }

  .comparisonTable th,
  .comparisonTable td {
    padding: 0.6rem 0.4rem;
    font-size: 0.8rem;
  }

  .sectionTitle {
    font-size: 1.6rem;
  }

  .sectionDescription {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .responsiveContainer {
    padding: 0 0.75rem;
  }

  /* Adjust table for small screens */
  .comparisonTable td:not(.featureCell) {
    font-size: 1rem;
    padding: 0.5rem 0.3rem;
  }

  .featureHeader {
    width: 40%;
  }

  /* Adjust column widths for small screens */
  .comparisonTable th:not(.featureHeader),
  .comparisonTable td:not(.featureCell) {
    width: calc((100% - 40%) / var(--competitor-count, 4));
  }
}
