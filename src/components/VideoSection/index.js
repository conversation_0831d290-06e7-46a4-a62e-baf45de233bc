import React from 'react';
import clsx from 'clsx';
import Translate from '@docusaurus/Translate';
import styles from './index.module.css';

import Heading from '@theme/Heading';
import Link from '@docusaurus/Link';

/**
 * VideoSection component
 *
 * @param {Object} props - Component props
 * @param {string} props.page - Page identifier for translation keys (e.g., 'homepage', 'aiflow')
 * @param {string} props.videoId - YouTube video ID
 * @param {string} props.titleTranslateId - Optional custom title translation ID
 * @param {string} props.descriptionTranslateId - Optional custom description translation ID
 * @param {string} props.ctaTranslateId - Optional custom CTA button translation ID
 * @param {string} props.ctaUrl - Optional custom CTA button URL
 * @param {Array} props.customFeatures - Optional custom features to override defaults
 */
function VideoSection({
  page = 'homepage',
  videoId = 'tPjuWOjpJIs',
  titleTranslateId = null,
  descriptionTranslateId = null,
  ctaTranslateId = null,
  ctaUrl = 'https://app.funblocks.net/#/login?source=flow',
  bg='#f0f7ff',
  customFeatures = null
}) {
  // Default features if no custom ones are provided
  const defaultFeatures = [
    {
      icon: '🔍',
      title: <Translate id={`${page}.video.feature1.title`}>Explore</Translate>,
      description: <Translate id={`${page}.video.feature1.description`}>
        See how to explore complex topics visually with AI assistance
      </Translate>
    },
    {
      icon: '🌟',
      title: <Translate id={`${page}.video.feature2.title`}>Think</Translate>,
      description: <Translate id={`${page}.video.feature2.description`}>
        Learn how to enhance your thinking with visual frameworks
      </Translate>
    },
    {
      icon: '✨',
      title: <Translate id={`${page}.video.feature3.title`}>Create</Translate>,
      description: <Translate id={`${page}.video.feature3.description`}>
        Discover how to transform ideas into professional deliverables
      </Translate>
    }
  ];

  // Use custom features if provided, otherwise use defaults
  const features = customFeatures || defaultFeatures;

  return (
    <section id="video-demo" className={styles.videoSection} style={{ background: bg }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id={titleTranslateId || `${page}.video.title`}>See FunBlocks AIFlow in Action</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id={descriptionTranslateId || `${page}.video.description`}>
            Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate
          </Translate>
        </p>

        <div className={styles.videoContainer}>
          <div className={styles.videoWrapper}>
            <iframe
              className={styles.videoFrame}
              src={`https://www.youtube.com/embed/${videoId}`}
              title="FunBlocks AIFlow Demo"
              border="0"
              style={{ border: 0 }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        </div>

        <div className={styles.videoFeatures}>
          {features.map((feature, index) => (
            <div key={index} className={styles.featureItem}>
              <div className={styles.featureIcon}>{feature.icon}</div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
            </div>
          ))}
        </div>

        <div className={styles.videoCta}>
          <Link
            className={clsx('button', styles.btn)}
            to="#"
            onClick={() => window.open(ctaUrl, '_blank')}
          >
            <Translate id={ctaTranslateId || `${page}.video.cta`}>Try It Yourself</Translate>
          </Link>
        </div>
      </div>
    </section>
  );
}

export default VideoSection;
