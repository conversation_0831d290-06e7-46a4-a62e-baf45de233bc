"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[62042],{9655:(e,i,s)=>{s.r(i),s.d(i,{default:()=>F});var n=s(96540),t=s(34164),a=s(56289),o=(s(40797),s(30300)),r=s(50539),c=s(27143),l=s(9303),d=s(21099);const h={learningCycle:"learningCycle_Gwys",cycleIntro:"cycleIntro_ujIw",cycleTitle:"cycleTitle_fuJ5",cycleDescription:"cycleDescription_zVsT",cycleSteps:"cycleSteps_cdjY",cycleStep:"cycleStep_P0tq",stepNumber:"stepNumber_dQHM",stepContent:"stepContent_iUYB",thinkingTools:"thinkingTools_dBT7",toolsTitle:"toolsTitle_yYl0",toolsGrid:"toolsGrid_sRJv",toolCard:"toolCard_LupR",toolIcon:"toolIcon_k7xq",learningQuote:"learningQuote_Kvjs",thinkingVisual:"thinkingVisual_hmwr",statsSection:"statsSection_yiun",statsGrid:"statsGrid_TlND",statCard:"statCard_o0kD",statNumber:"statNumber_EQOc",statLabel:"statLabel_o24y",demoSection:"demoSection_a_Dp",demoContainer:"demoContainer_BeSu",demoVideo:"demoVideo_Y0Rb",demoImage:"demoImage_Fj4Z",demoOverlay:"demoOverlay_zzTk",demoButton:"demoButton_WKLW"};var m=s(26167),u=s(87263),p=s(79912),g=s(81896),f=s(51971),x=s(23177),j=s(78905),w=s(46258),A=s(74848);function k(e){let{toApp:i}=e;return(0,A.jsx)("section",{className:(0,t.A)(d.A.hero,d.A.pageSection),style:{backgroundColor:"#f0f7ff"},children:(0,A.jsxs)("div",{className:"container",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[(0,A.jsx)(l.A,{as:"h1",className:d.A.slidesTitle,children:(0,A.jsx)(r.A,{id:"aidocs.masthead.title",children:"Write Smarter, Think Deeper"})}),(0,A.jsx)("h2",{className:d.A.slidesSubtitle,children:(0,A.jsx)(r.A,{id:"aidocs.masthead.subtitle",children:"Elevate your writing with AI-powered document editor and critical thinking assistance"})}),(0,A.jsx)("div",{className:d.A.heroButtons,children:(0,A.jsx)(a.A,{className:(0,t.A)("button",d.A.btn),to:"#",onClick:()=>i(),children:(0,A.jsx)(r.A,{id:"aidocs.masthead.cta",children:"Try for Free"})})})]})})}function b(e){let{setShowImageSrc:i}=e;return(0,A.jsx)("section",{id:"features",className:d.A.slidesFeatureSection,children:(0,A.jsxs)("div",{className:d.A.slidesContainer,children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.features.title",children:"Key Features"})}),(0,A.jsx)("div",{children:[{name:"aidocs.features.item1.name",description:"aidocs.features.item1.description",image:"/img/portfolio/thumbnails/ai_writer_block_editor.png",alt:"Block-based document editor similar to Notion"},{name:"aidocs.features.item2.name",description:"aidocs.features.item2.description",image:"/img/portfolio/thumbnails/ai_writer_editing_assistant.png",alt:"AI writing assistant for content creation"},{name:"aidocs.features.item3.name",description:"aidocs.features.item3.description",image:"/img/portfolio/thumbnails/ai_writer_infographic_assistant.png",alt:"Critical thinking enhancement features"},{name:"aidocs.features.item4.name",description:"aidocs.features.item4.description",image:"/img/portfolio/thumbnails/ai_writer_critical_thinking_assistant.png",alt:"Document organization and linking capabilities"},{name:"aidocs.features.item5.name",description:"aidocs.features.item5.description",image:"/img/portfolio/thumbnails/ai_writer_workspace.png",alt:"Document organization and linking capabilities"}].map(((e,s)=>(0,A.jsxs)("div",{className:d.A.slidesRow,children:[(0,A.jsx)("div",{className:(0,t.A)(d.A.slidesCol8,{[d.A.order2]:s%2==0}),children:(0,A.jsx)("img",{className:d.A.docsFeatureImage,src:e.image,alt:e.alt,onClick:()=>i(e.image.replace("thumbnails","fullsize"))})}),(0,A.jsxs)("div",{className:(0,t.A)(d.A.slidesCol4,{[d.A.order1]:s%2==0}),style:{justifyContent:"center",display:"flex",flexDirection:"column"},children:[(0,A.jsx)(l.A,{as:"h3",className:d.A.cardTitle,children:(0,A.jsx)(r.A,{id:e.name,children:e.name})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:e.description,children:e.description})})]})]},s)))})]})})}function _(e){let{setShowImageSrc:i}=e;return(0,A.jsx)("section",{id:"ai-assistant",className:d.A.featureSection,style:{backgroundColor:"aliceblue"},children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.title",children:"AI Writing Assistant: Your Thinking Partner"})}),(0,A.jsx)("p",{className:d.A.sectionDescription,children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.subtitle",children:"More than just a writing tool - an AI that enhances your critical thinking"})}),(0,A.jsxs)("div",{className:d.A.featureGrid,children:[(0,A.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,A.jsx)(l.A,{as:"h3",children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.title",children:"Enhance Critical Thinking"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.description",children:"FunBlocks AI Docs goes beyond grammar fixes and style improvements to help you think more clearly and write more persuasively."})}),(0,A.jsxs)("ul",{className:d.A.featureList,children:[(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.point1",children:"Identify cognitive biases in your writing"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.point2",children:"Highlight logical fallacies and suggest improvements"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.point3",children:"Strengthen arguments by addressing counterpoints"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ai-assistant.critical-thinking.point4",children:"Analyze the clarity and coherence of your reasoning"})})]})]}),(0,A.jsx)("div",{style:{cursor:"pointer",flex:3},children:(0,A.jsx)("img",{className:d.A.docsFeatureImage,onClick:()=>i("/img/portfolio/fullsize/ai_writer_critical_thinking_assistant.png"),id:"aidocs-critical-thinking",alt:"AI-powered critical thinking enhancement features",src:"/img/portfolio/thumbnails/ai_writer_critical_thinking_assistant.png"})})]})]})})}function y(e){let{setShowImageSrc:i}=e;return(0,A.jsx)("section",{id:"writing-assistant",className:d.A.featureSection,children:(0,A.jsx)("div",{className:"container",children:(0,A.jsxs)("div",{className:d.A.featureGrid,children:[(0,A.jsx)("div",{style:{cursor:"pointer",flex:3},children:(0,A.jsx)("img",{className:d.A.docsFeatureImage,onClick:()=>i("/img/portfolio/fullsize/ai_writer_editing_assistant.png"),id:"aidocs-writing-assistant",alt:"AI-powered writing assistance features",src:"/img/portfolio/thumbnails/ai_writer_editing_assistant.png"})}),(0,A.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,A.jsx)(l.A,{as:"h3",children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.title",children:"Smart Writing Tools"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.description",children:"Create high-quality content efficiently with powerful AI-powered writing tools."})}),(0,A.jsxs)("ul",{className:d.A.featureList,children:[(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.point1",children:"Generate complete documents from a simple topic or outline"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.point2",children:"Rewrite and refine selected text with targeted instructions"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.point3",children:"Intelligent continuation suggestions based on your writing style"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.point4",children:"Grammar and style correction with detailed explanations"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.writing-assistant.point5",children:"Grammar and style correction with detailed explanations"})})]})]})]})})})}function I(e){let{setShowImageSrc:i}=e;return(0,A.jsx)("section",{id:"ecosystem-integration",className:d.A.featureSection,style:{backgroundColor:"cornsilk"},children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.title",children:"Seamless FunBlocks Ecosystem Integration"})}),(0,A.jsx)("p",{className:d.A.sectionDescription,children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.subtitle",children:"Part of a powerful AI workspace that enhances your entire workflow"})}),(0,A.jsxs)("div",{className:d.A.featureGrid,children:[(0,A.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,A.jsx)(l.A,{as:"h3",children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.title",children:"Complete Workflow Integration"})}),(0,A.jsxs)("ul",{className:d.A.featureList,children:[(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.point1",children:"Convert AIFlow mind maps directly into structured documents with one click"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.point2",children:"Transform any document into professional slides for presentations"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.point3",children:"Turn document content into visual mind maps for deeper exploration"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.point4",children:"Create a comprehensive workspace by linking documents, mind maps and presentations"})}),(0,A.jsx)("li",{children:(0,A.jsx)(r.A,{id:"aidocs.ecosystem.workflow.point5",children:"Create a comprehensive workspace by linking documents, mind maps and presentations"})})]})]}),(0,A.jsx)("div",{style:{cursor:"pointer",flex:3},children:(0,A.jsx)("img",{className:d.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/ai_workspace.png"),id:"aidocs-ecosystem",alt:"FunBlocks ecosystem integration features",src:"/img/portfolio/thumbnails/ai_workspace.png"})})]})]})})}function v(e){let{setShowImageSrc:i}=e;return(0,A.jsx)("section",{id:"thinking-enhancement",className:d.A.featureSection,style:{backgroundColor:"#f5f8ff"},children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.title",children:"Writing as a Thinking Tool"})}),(0,A.jsx)("p",{className:d.A.sectionDescription,children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.description",children:"Writing is not just for output\u2014it's a powerful method for learning and enhancing cognitive abilities. FunBlocks AI ecosystem provides a complete solution for your thinking journey."})}),(0,A.jsxs)("div",{className:h.learningCycle,children:[(0,A.jsxs)("div",{className:h.cycleIntro,children:[(0,A.jsx)(l.A,{as:"h3",className:h.cycleTitle,children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.title",children:"The Complete Thinking Cycle"})}),(0,A.jsx)("p",{className:h.cycleDescription,children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.description",children:"FunBlocks AI integrates all stages of the thinking process\u2014from initial ideas to structured knowledge\u2014creating a powerful ecosystem for learning and intellectual growth."})})]}),(0,A.jsxs)("div",{className:h.cycleSteps,children:[(0,A.jsxs)("div",{className:h.cycleStep,children:[(0,A.jsx)("div",{className:h.stepNumber,children:"1"}),(0,A.jsxs)("div",{className:h.stepContent,children:[(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step1.title",children:"Explore & Discover"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step1.description",children:"Begin with AI-powered brainstorming to explore topics and generate initial ideas"})})]})]}),(0,A.jsxs)("div",{className:h.cycleStep,children:[(0,A.jsx)("div",{className:h.stepNumber,children:"2"}),(0,A.jsxs)("div",{className:h.stepContent,children:[(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step2.title",children:"Visualize & Connect"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step2.description",children:"Organize thoughts with mind maps to see relationships and identify patterns"})})]})]}),(0,A.jsxs)("div",{className:h.cycleStep,children:[(0,A.jsx)("div",{className:h.stepNumber,children:"3"}),(0,A.jsxs)("div",{className:h.stepContent,children:[(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step3.title",children:"Create & Develop"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step3.description",children:"Transform ideas into structured documents with creative thinking tools"})})]})]}),(0,A.jsxs)("div",{className:h.cycleStep,children:[(0,A.jsx)("div",{className:h.stepNumber,children:"4"}),(0,A.jsxs)("div",{className:h.stepContent,children:[(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step4.title",children:"Analyze & Refine"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.cycle.step4.description",children:"Apply critical thinking to strengthen arguments and eliminate biases"})})]})]})]})]}),(0,A.jsxs)("div",{className:h.thinkingTools,children:[(0,A.jsx)(l.A,{as:"h3",className:h.toolsTitle,children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.title",children:"AI-Powered Thinking Tools"})}),(0,A.jsxs)("div",{className:h.toolsGrid,children:[(0,A.jsxs)("div",{className:h.toolCard,children:[(0,A.jsx)("div",{className:h.toolIcon,children:"\u26a1"}),(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.brainstorming.title",children:"AI-Powered Brainstorming"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.brainstorming",children:"Generate ideas using AI-guided frameworks like SCAMPER, Six Thinking Hats, and First Principles thinking. Expand seed concepts into comprehensive outlines."})})]}),(0,A.jsxs)("div",{className:h.toolCard,children:[(0,A.jsx)("div",{className:h.toolIcon,children:"\ud83d\uddfa\ufe0f"}),(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.mindmap.title",children:"Mind Map Integration"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.mindmap",children:"Visualize complex relationships between ideas with one-click conversion between documents and mind maps. Enhance comprehension and retention through visual thinking."})})]}),(0,A.jsxs)("div",{className:h.toolCard,children:[(0,A.jsx)("div",{className:h.toolIcon,children:"\ud83d\udca1"}),(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.creative-thinking.title",children:"Creative Thinking Tools"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.creative",children:"View content from multiple perspectives, generate analogies and metaphors, and challenge limiting assumptions to discover innovative solutions and insights."})})]}),(0,A.jsxs)("div",{className:h.toolCard,children:[(0,A.jsx)("div",{className:h.toolIcon,children:"\u2696\ufe0f"}),(0,A.jsx)(l.A,{as:"h4",children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.critical.title",children:"Critical Thinking Assistant"})}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.tools.critical",children:"Identify cognitive biases, highlight logical fallacies, strengthen arguments by addressing counterpoints, and analyze the clarity of your reasoning."})})]})]})]}),(0,A.jsx)("div",{className:h.learningQuote,children:(0,A.jsx)("blockquote",{children:(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:"aidocs.thinking-enhancement.quote",children:'"Writing is not just communicating ideas; it\'s a powerful tool for developing them. FunBlocks AI transforms writing from mere documentation into an active learning process."'})})})})]})})}function C(){return(0,A.jsx)("section",{id:"use-cases",className:d.A.useCases,children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.use-cases.title",children:"Use Cases"})}),(0,A.jsx)("p",{className:d.A.sectionDescription,children:(0,A.jsx)(r.A,{id:"aidocs.use-cases.description",children:"FunBlocks AI Docs adapts to diverse content creation scenarios, enhancing both productivity and quality"})}),(0,A.jsx)("div",{className:d.A.twoColumnGrid,children:[{icon:"\ud83d\udcda",titleId:"aidocs.use-cases.case1.title",descriptionId:"aidocs.use-cases.case1.description"},{icon:"\ud83d\udcdd",titleId:"aidocs.use-cases.case2.title",descriptionId:"aidocs.use-cases.case2.description"},{icon:"\ud83c\udf93",titleId:"aidocs.use-cases.case3.title",descriptionId:"aidocs.use-cases.case3.description"},{icon:"\ud83d\udcbc",titleId:"aidocs.use-cases.case4.title",descriptionId:"aidocs.use-cases.case4.description"},{icon:"\ud83d\udcca",titleId:"aidocs.use-cases.case5.title",descriptionId:"aidocs.use-cases.case5.description"},{icon:"\ud83d\udca1",titleId:"aidocs.use-cases.case6.title",descriptionId:"aidocs.use-cases.case6.description"}].map(((e,i)=>(0,A.jsxs)("div",{className:d.A.useCaseCard,children:[(0,A.jsxs)("div",{className:d.A.cardTitle,children:[(0,A.jsx)("div",{className:d.A.useCaseIcon,children:e.icon}),(0,A.jsx)("span",{children:(0,A.jsx)(r.A,{id:e.titleId,children:"Case Title"})})]}),(0,A.jsx)("p",{children:(0,A.jsx)(r.A,{id:e.descriptionId,children:"Case description"})})]},i)))})]})})}function N(){return(0,A.jsx)("section",{id:"stats",className:h.statsSection,children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(l.A,{as:"h2",className:d.A.sectionTitle,children:(0,A.jsx)(r.A,{id:"aidocs.stats.title",children:"Trusted by Professionals Worldwide"})}),(0,A.jsx)("p",{className:d.A.sectionDescription,children:(0,A.jsx)(r.A,{id:"aidocs.stats.description",children:"Join thousands of professionals who use FunBlocks AI Docs to enhance their writing and thinking"})}),(0,A.jsxs)("div",{className:h.statsGrid,children:[(0,A.jsxs)("div",{className:h.statCard,children:[(0,A.jsx)("div",{className:h.statNumber,children:"100,000+"}),(0,A.jsx)("div",{className:h.statLabel,children:(0,A.jsx)(r.A,{id:"aidocs.stats.users",children:"Active Users"})})]}),(0,A.jsxs)("div",{className:h.statCard,children:[(0,A.jsx)("div",{className:h.statNumber,children:"1.2M+"}),(0,A.jsx)("div",{className:h.statLabel,children:(0,A.jsx)(r.A,{id:"aidocs.stats.documents",children:"Documents Created"})})]}),(0,A.jsxs)("div",{className:h.statCard,children:[(0,A.jsx)("div",{className:h.statNumber,children:"40%"}),(0,A.jsx)("div",{className:h.statLabel,children:(0,A.jsx)(r.A,{id:"aidocs.stats.timeSaved",children:"Average Time Saved"})})]}),(0,A.jsxs)("div",{className:h.statCard,children:[(0,A.jsx)("div",{className:h.statNumber,children:"4.8/5"}),(0,A.jsx)("div",{className:h.statLabel,children:(0,A.jsx)(r.A,{id:"aidocs.stats.satisfaction",children:"User Satisfaction"})})]})]})]})})}function S(){const e={"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Docs",applicationCategory:"ProductivityApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},description:"AI-powered document editor with critical thinking, brainstorming, and mind mapping capabilities to enhance your writing and learning process.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"1250",bestRating:"5",worstRating:"1"},featureList:["AI-powered writing assistant","Critical thinking enhancement","Brainstorming tools","Mind map integration","Creative thinking tools","Block-based editor"],screenshot:"https://www.funblocks.net/img/portfolio/fullsize/ai_writer_editor.png",url:"undefined"!=typeof window?window.location.href:"https://www.funblocks.net/aidocs",keywords:"AI document editor, critical thinking, brainstorming, mind mapping, creative thinking, learning tool",applicationSubCategory:"Document Editor",author:{"@type":"Organization",name:"FunBlocks",url:"https://www.funblocks.net"}};return(0,A.jsx)(c.A,{children:(0,A.jsx)("script",{type:"application/ld+json",children:JSON.stringify(e)})})}function T(){return(0,A.jsx)(c.A,{children:(0,A.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AI Docs and how does it improve document writing and editing?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Docs is a comprehensive AI-powered document editor that combines Notion-like block editing with advanced artificial intelligence to enhance your writing process, critical thinking abilities, and overall productivity. The platform features a flexible block-based editor similar to Notion, powerful AI writing assistance, critical thinking enhancement tools that identify biases and logical fallacies, and seamless integration with other FunBlocks products like AIFlow mind maps and AI Slides."}},{"@type":"Question",name:"How does the AI Writing Assistant in FunBlocks AI Docs help improve my writing quality and efficiency?",acceptedAnswer:{"@type":"Answer",text:"The AI Writing Assistant in FunBlocks AI Docs functions as your personal thinking partner to elevate both writing quality and efficiency. It provides four key capabilities: 1) Generate complete documents from scratch based on simple topics or outlines, 2) Intelligently rewrite and refine selected text based on specific instructions, 3) Offer smart continuation suggestions that match your personal writing style, and 4) Provide comprehensive grammar and style corrections with detailed explanations."}},{"@type":"Question",name:"What makes the Critical Thinking Enhancement feature in FunBlocks AI Docs unique compared to other writing tools?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Docs' Critical Thinking Enhancement feature goes far beyond what traditional writing tools offer by focusing on the quality of your reasoning, not just your writing mechanics. This unique feature helps you think more clearly and write more persuasively through four advanced capabilities: 1) Identifying specific cognitive biases in your writing that might weaken your arguments, 2) Highlighting logical fallacies and providing clear suggestions for improvement, 3) Strengthening your arguments by identifying and addressing potential counterarguments, and 4) Analyzing the clarity and coherence of your reasoning."}},{"@type":"Question",name:"How does FunBlocks AI Docs integrate with AIFlow mind maps and AI Slides to create a complete workflow system?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Docs deeply integrates with AIFlow mind maps and AI Slides through a comprehensive ecosystem approach, creating a seamless all-in-one workflow. This integration enables four powerful workflow capabilities: 1) One-click conversion of AIFlow mind maps directly into documents with appropriate hierarchy and organization, 2) Automatic transformation of any document into professional presentation slides suitable for meetings or conferences, 3) Quick conversion of document content into visual mind maps for concept exploration and relationship mapping, and 4) Creation of a unified knowledge workspace through bidirectional linking between documents, mind maps, and presentations."}},{"@type":"Question",name:"How can FunBlocks AI Docs help with brainstorming and creative thinking?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Docs transforms writing from mere output into a powerful learning and thinking tool. It provides integrated brainstorming tools that help generate, organize, and develop ideas using AI-guided frameworks like SCAMPER and Six Thinking Hats. The creative thinking tools allow you to view content from different perspectives, generate analogies and metaphors, and challenge limiting assumptions. With seamless mind map integration, you can visualize complex relationships between ideas and enhance comprehension through visual thinking."}}]})})})}function B(){const e={"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"https://www.funblocks.net"},{"@type":"ListItem",position:2,name:"Products",item:"https://www.funblocks.net/products"},{"@type":"ListItem",position:3,name:"FunBlocks AI Docs",item:"undefined"!=typeof window?window.location.href:"https://www.funblocks.net/aidocs"}]};return(0,A.jsx)(c.A,{children:(0,A.jsx)("script",{type:"application/ld+json",children:JSON.stringify(e)})})}function F(){const[e,i]=(0,n.useState)(null);function s(){!function(e){window.open().location.href=e}(`https://app.${window.location.hostname.includes("funblocks")?window.location.hostname.replace("www.",""):"funblocks.net"}/#/login?source=aidocs`)}return(0,A.jsxs)(o.A,{title:(0,r.T)({id:"aidocs.head.title",message:"FunBlocks AI Docs: AI-powered Block Editor & Critical Thinking Assistant | Brainstorming & Mind Mapping"}),description:(0,r.T)({id:"aidocs.head.description",message:"FunBlocks AI Docs combines Notion-like editing with AI assistance to enhance your writing, critical thinking, and brainstorming capabilities. Create better content faster with integrated mind mapping and creative thinking tools."}),children:[(0,A.jsx)(S,{}),(0,A.jsx)(T,{}),(0,A.jsx)(B,{}),(0,A.jsx)(k,{toApp:s}),(0,A.jsxs)("main",{children:[(0,A.jsx)(x.A,{page:"aidocs",feature:"intro",pointNos:[1,2,3,4,5],style:{backgroundColor:"lightcyan"},imageElement:(0,A.jsx)("div",{style:{flex:4,cursor:"pointer"},children:(0,A.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/ai_writer_editor.png",alt:"FunBlocks AI Docs: Notion-style block editor with AI assistant",onClick:()=>i("/img/portfolio/fullsize/ai_writer_editor.png")})})}),(0,A.jsx)(b,{setShowImageSrc:i}),(0,A.jsx)(_,{setShowImageSrc:i}),(0,A.jsx)(y,{setShowImageSrc:i}),(0,A.jsx)(v,{setShowImageSrc:i}),(0,A.jsx)(I,{setShowImageSrc:i}),(0,A.jsx)(x.A,{page:"aidocs",feature:"organization",pointNos:[1,2,3,4],style:{backgroundColor:"lavender"},imageElement:(0,A.jsx)("div",{style:{flex:4,cursor:"pointer"},children:(0,A.jsx)("img",{className:d.A.docsFeatureImage,src:"/img/portfolio/thumbnails/ai_writer_workspace.png",alt:"FunBlocks AI Docs: Notion-style block editor with AI assistant",onClick:()=>i("/img/portfolio/fullsize/ai_writer_workspace.png")})})}),(0,A.jsx)(w.A,{page:"aidocs",competitors:{funblocks:{label:(0,A.jsx)(r.A,{id:"aidocs.comparison.funblocksHeader",children:"FunBlocks AI Docs"}),isHighlighted:!0},googleDocs:{label:(0,A.jsx)(r.A,{id:"aidocs.comparison.googleDocsHeader",children:"Google Docs"}),isHighlighted:!1},notion:{label:(0,A.jsx)(r.A,{id:"aidocs.comparison.notionHeader",children:"Notion"}),isHighlighted:!1},grammarly:{label:(0,A.jsx)(r.A,{id:"aidocs.comparison.grammarly",children:"Grammarly"}),isHighlighted:!1}},customData:[{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature1",children:"Block-Based Editor"}),funblocks:!0,googleDocs:"Limited",notion:!0,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature2",children:"AI Writing Assistant"}),funblocks:!0,googleDocs:"Limited",notion:!0,grammarly:!0},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature3",children:"Critical Thinking Enhancement"}),funblocks:!0,googleDocs:!1,notion:!1,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature4",children:"Brainstorming Tools"}),funblocks:!0,googleDocs:!1,notion:!1,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature5",children:"Mind Map Integration"}),funblocks:!0,googleDocs:!1,notion:!1,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature6",children:"Infographics Generation"}),funblocks:!0,googleDocs:!1,notion:!1,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature7",children:"Creative Thinking Tools"}),funblocks:!0,googleDocs:!1,notion:!1,grammarly:!1},{feature:(0,A.jsx)(r.A,{id:"aidocs.comparison.feature8",children:"Document Organization"}),funblocks:!0,googleDocs:"Basic",notion:!0,grammarly:!1}]}),(0,A.jsx)(C,{}),(0,A.jsx)(N,{}),(0,A.jsx)(f.A,{avatars:["\ud83d\udc69\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc69\u200d\ud83d\udcbb","\ud83d\udc68\u200d\ud83c\udf93","\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83c\udfeb"],page:"aidocs"}),(0,A.jsx)(j.A,{toApp:s,page:"aidocs"}),(0,A.jsx)(u.A,{page:"aidocs",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8","q9","q10","q11","q12"]})]}),(0,A.jsx)(m.A,{}),e&&(0,A.jsx)(p.A,{imageSrc:e,setImageSrc:i}),(0,A.jsx)(g.A,{page:"aidocs"})]})}},21099:(e,i,s)=>{s.d(i,{A:()=>n});const n={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},23177:(e,i,s)=>{s.d(i,{A:()=>c});var n=s(96540),t=s(50539);const a={mainNav:"mainNav_SYRv",headerContainer:"headerContainer_a2TU",logo:"logo_c69e",navLinks:"navLinks_Sixa",languageSelector:"languageSelector__dBh",hero:"hero_PrCm",heroContent:"heroContent_XkQ_",heroSubtitle:"heroSubtitle_ICC1",heroButtons:"heroButtons_aEqQ",heroImage:"heroImage_eIAu",btn:"btn_OEYT",btnSecondary:"btnSecondary_S33h",btnSm:"btnSm_oVT3",beyondChatgpt:"beyondChatgpt_FCrm",sectionTitle:"sectionTitle_s7rk",sectionDescription:"sectionDescription_bUKO",twoColumnGrid:"twoColumnGrid_A0Q4",benefitsContainer:"benefitsContainer_f82T",featureSection:"featureSection_YLp6",featureGrid:"featureGrid_iPVW",featureContent:"featureContent_CE7A",featureList:"featureList_XB6o",featureImage:"featureImage_VE23",imageLeft:"imageLeft_WPA6",imageRight:"imageRight_l9wy",fullWidthImage:"fullWidthImage_QGUC",multiModelAdvantage:"multiModelAdvantage_gag0",modelLogosContainer:"modelLogosContainer_ZsBe",modelLogoItem:"modelLogoItem_PSuJ",modelLogo:"modelLogo_ishV",modelName:"modelName_aa8M",advantageText:"advantageText_Dkdl",useCases:"useCases_rrcz",useCasesGrid:"useCasesGrid__MUR",useCaseCard:"useCaseCard_jbjO",useCaseIcon:"useCaseIcon_Z7j9",workspaceSection:"workspaceSection_t8km",ctaSection:"ctaSection_Z6zg",ctaButtons:"ctaButtons_3xcx",ctaBtn:"ctaBtn_tJ_L",toolsList:"toolsList_NtZD",pageSection:"pageSection_W06t"};var o=s(9303),r=s(74848);const c=function(e){let{page:i,feature:s,pointNos:c,imageElement:l,imageToRight:d,style:h}=e;const m=(0,n.useMemo)((()=>c.map((e=>({title:`${i}.${s}.point${e}.name`,description:`${i}.${s}.point${e}.description`})))),[i,s,c]),u=`${a.featureGrid} ${d?a.imageRight:a.imageLeft}`;return(0,r.jsx)("section",{className:a.featureSection,style:h,children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsx)(o.A,{as:"h2",className:a.sectionTitle,children:(0,r.jsx)(t.A,{id:`${i}.${s}.title`,children:"Beyond ChatGPT"})}),(0,r.jsx)("p",{className:a.sectionDescription,children:(0,r.jsx)(t.A,{id:`${i}.${s}.description`,children:"Discover innovative ways to engage with AI beyond text. Visualize your thoughts and AI outputs in formats that enhance your cognitive process."})}),(0,r.jsxs)("div",{className:u,children:[l,(0,r.jsx)("div",{className:a.featureContent,children:m.map(((e,i)=>(0,r.jsx)("div",{className:a.benefitItem,children:(0,r.jsxs)("div",{children:[(0,r.jsx)(o.A,{as:"h3",children:(0,r.jsx)(t.A,{id:e.title,children:e.title})}),(0,r.jsx)("p",{children:(0,r.jsx)(t.A,{id:e.description,children:e.description})})]})},i)))})]})]})})}},26167:(e,i,s)=>{s.d(i,{A:()=>l});const n="footer_m3PR",t="footerContainer_g8s3",a="footerLinks_EjWI",o="toolsGrid_N_gp",r="copyright_zlJy";var c=s(74848);const l=function(){return(0,c.jsx)("footer",{className:n,children:(0,c.jsxs)("div",{className:"container",children:[(0,c.jsxs)("div",{className:t,children:[(0,c.jsxs)("div",{className:a,style:{marginRight:"20px"},children:[(0,c.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,c.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,c.jsxs)("div",{className:a,children:[(0,c.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,c.jsxs)("ul",{children:[(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,c.jsxs)("div",{className:a,children:[(0,c.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,c.jsxs)("ul",{children:[(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})})]})]}),(0,c.jsxs)("div",{className:a,children:[(0,c.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,c.jsx)("ul",{children:(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,c.jsx)("div",{className:t,children:(0,c.jsxs)("div",{className:a,children:[(0,c.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,c.jsxs)("div",{className:o,children:[(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Thinking Coach"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,c.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,c.jsx)("div",{className:r,children:(0,c.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},46258:(e,i,s)=>{s.d(i,{A:()=>w});s(96540);var n=s(34164),t=s(50539);const a="comparisonSection_xGN0",o="responsiveContainer_LWkJ",r="sectionTitle_WEFW",c="sectionDescription_C78Y",l="tableContainer_XXdk",d="comparisonTable_nUFG",h="featureHeader_MZvb",m="featureCell_N7ZK",u="funblocksHeader_W9br",p="funblocksCell_mVK7",g="comparisonNote_BsSN",f="scrollIndicator_qOFX";var x=s(9303),j=s(74848);const w=function(e){let{page:i="homepage",customData:s=null,titleTranslateId:w=null,descriptionTranslateId:A=null,noteTranslateId:k=null,competitors:b=null}=e;const _={funblocks:{label:(0,j.jsx)(t.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,j.jsx)(t.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,j.jsx)(t.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,j.jsx)(t.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},y=b||_,I=[{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,j.jsx)(t.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],v=s||I;return(0,j.jsx)("section",{id:"comparison",className:a,children:(0,j.jsxs)("div",{className:(0,n.A)("container",o),children:[(0,j.jsx)(x.A,{as:"h2",className:r,children:(0,j.jsx)(t.A,{id:w||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,j.jsx)("p",{className:c,children:(0,j.jsx)(t.A,{id:A||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,j.jsx)("div",{className:f,children:(0,j.jsx)(t.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,j.jsx)("div",{className:l,children:(0,j.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(y).length},children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{children:[(0,j.jsx)("th",{className:h,children:(0,j.jsx)(t.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(y).map((e=>{let[i,s]=e;return(0,j.jsx)("th",{className:s.isHighlighted?u:void 0,children:s.label},i)}))]})}),(0,j.jsx)("tbody",{children:v.map(((e,i)=>(0,j.jsxs)("tr",{children:[(0,j.jsx)("td",{className:m,children:e.feature}),Object.entries(y).map((i=>{let[s,n]=i;return(0,j.jsx)("td",{className:n.isHighlighted?p:void 0,children:!0===e[s]?"\u2705":!1===e[s]?"\u274c":e[s]},s)}))]},i)))})]})}),(0,j.jsx)("div",{className:g,children:(0,j.jsx)("p",{children:(0,j.jsx)(t.A,{id:k||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,s)=>{s.d(i,{A:()=>g});var n=s(96540),t=s(50539);const a="sectionTitle_pRDY",o="sectionDescription_GyST",r="benefitsContainer_jm1z",c="testimonialsSection_bcfx",l="testimonialCard_jqt8",d="testimonialHeader_K3A9",h="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var u=s(9303),p=(s(56289),s(74848));const g=function(e){let{page:i,avatars:s}=e;const g=(0,n.useMemo)((()=>s.map(((e,s)=>({avatar:e,nameId:`${i}.testimonials.user${s+1}.name`,roleId:`${i}.testimonials.user${s+1}.role`,textId:`${i}.testimonials.user${s+1}.text`})))),[i,s]);return(0,p.jsx)("section",{id:"testimonials",className:c,children:(0,p.jsxs)("div",{className:"container",children:[(0,p.jsx)(u.A,{as:"h2",className:a,children:(0,p.jsx)(t.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,p.jsx)("p",{className:o,children:(0,p.jsx)(t.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,p.jsx)("div",{className:r,children:g?.map(((e,i)=>(0,p.jsxs)("div",{className:l,children:[(0,p.jsxs)("div",{className:d,children:[(0,p.jsx)("div",{className:h,children:(0,p.jsx)("span",{children:e.avatar})}),(0,p.jsxs)("div",{className:m,children:[(0,p.jsx)("h4",{children:(0,p.jsx)(t.A,{id:e.nameId,children:e.nameId})}),(0,p.jsx)("p",{children:(0,p.jsx)(t.A,{id:e.roleId,children:e.roleId})})]})]}),(0,p.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,p.jsx)("p",{children:(0,p.jsx)(t.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},78905:(e,i,s)=>{s.d(i,{A:()=>m});s(96540);var n=s(34164),t=s(50539);const a="btn_4iM2",o="ctaButtons_Cfhe",r="ctaBtn_Hq_p",c="ctaSection_vQl5";var l=s(9303),d=s(56289),h=s(74848);const m=function(e){let{page:i,toApp:s,customButtonText:m}=e;return(0,h.jsx)("section",{id:"cta",className:c,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(l.A,{as:"h2",children:(0,h.jsx)(t.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(t.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:o,children:(0,h.jsx)(d.A,{className:(0,n.A)(a,r),to:"#",onClick:()=>s(),children:m||(0,h.jsx)(t.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,s)=>{s.d(i,{A:()=>l});s(96540);var n=s(50539);const t="modal_osiT",a="modalImage_HWh8",o="close_Y6T6",r="zoomIndicator_r4Py";var c=s(74848);const l=function(e){let{imageSrc:i,setImageSrc:s}=e;const l=()=>{s(null)};return(0,c.jsxs)("div",{className:t,style:{display:"flex"},onClick:l,children:[(0,c.jsx)("span",{className:o,onClick:l,children:"\xd7"}),(0,c.jsx)("img",{className:a,src:i,alt:(0,n.T)({id:"modal.alt",message:"Enlarged view"})}),(0,c.jsx)("div",{className:r,children:(0,c.jsx)(n.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,s)=>{s.d(i,{A:()=>t});s(96540);var n=s(74848);const t=function(e){let{page:i}=e;const s=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",t=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${s?"source="+s+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:t}})})}},87263:(e,i,s)=>{s.d(i,{A:()=>f});var n=s(96540),t=s(34164),a=s(50539);const o="sectionTitle_gwu3",r="faqSection_DBlu",c="faqContainer_pGyA",l="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",u="faqAnswer_HbCX";var p=s(74848);function g(e){let{page:i,questionId:s,answerId:o}=e;const[r,c]=(0,n.useState)(!1);return(0,p.jsxs)("div",{className:(0,t.A)(l,{[m]:r}),children:[(0,p.jsxs)("div",{className:d,onClick:()=>{c(!r)},children:[(0,p.jsx)("span",{style:{fontWeight:"normal"},children:(0,p.jsx)(a.A,{id:`${i}.faq.${s}`})}),(0,p.jsx)("div",{className:h,style:{transform:r?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,p.jsx)("div",{className:u,style:{whiteSpace:"pre-line",display:r?"block":"none"},children:(0,p.jsx)(a.A,{id:`${i}.faq.${o}`})})]})}const f=function(e){let{page:i,faqIds:s}=e;return(0,p.jsx)("section",{id:"faqs",className:(0,t.A)("page-section",r),style:{backgroundColor:"var(--gray)"},children:(0,p.jsxs)("div",{className:"container",children:[(0,p.jsx)("h2",{className:o,children:(0,p.jsx)(a.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,p.jsx)("div",{className:c,children:s.map((e=>(0,p.jsx)(g,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);