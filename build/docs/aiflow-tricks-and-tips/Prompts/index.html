<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Prompts" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Creating Custom AI Applications | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Prompts"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" property="og:locale:alternate" content="zh"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Creating Custom AI Applications | FunBlocks AI"><meta data-rh="true" name="description" content="Building Your Own AI Apps"><meta data-rh="true" property="og:description" content="Building Your Own AI Apps"><link data-rh="true" rel="icon" href="/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Prompts"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Prompts" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Prompts" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Prompts" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/assets/css/styles.04092f1b.css">
<script src="/assets/js/runtime~main.adb7c2a2.js" defer="defer"></script>
<script src="/assets/js/main.049b8225.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/icon.png"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/slides">AI Slides</a><a class="navbar__item navbar__link" href="/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>English</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Prompts" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Prompts" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="Expand sidebar category &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="Collapse sidebar category &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/ai-tools">AI Tools</a><button aria-label="Expand sidebar category &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Creating Custom AI Applications</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Creating Custom AI Applications</h1></header>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="building-your-own-ai-apps">Building Your Own AI Apps<a href="#building-your-own-ai-apps" class="hash-link" aria-label="Direct link to Building Your Own AI Apps" title="Direct link to Building Your Own AI Apps">​</a></h2>
<p>FunBlocks AIFlow is an AI-native whiteboard application with powerful capabilities that complete the full cycle from generating creative ideas to organizing and producing finished work. Compared to using ChatGPT directly, AIFlow&#x27;s AI assistant offers numerous &quot;one-click&quot; features that require no prompt writing from users, as AIFlow has integrated high-quality prompts that save you time and lower the barrier to entry.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="what-if-the-built-in-ai-features-dont-meet-your-needs">What if the built-in AI features don&#x27;t meet your needs?<a href="#what-if-the-built-in-ai-features-dont-meet-your-needs" class="hash-link" aria-label="Direct link to What if the built-in AI features don&#x27;t meet your needs?" title="Direct link to What if the built-in AI features don&#x27;t meet your needs?">​</a></h3>
<p>AIFlow provides direct prompting functionality for node content. In the AIFlow AI assistant menu, the input box serves as both a menu search box and a direct prompt input box. Simply type your prompt here, press Enter or click the &quot;Confirm&quot; button, and AIFlow will send your prompt along with the node content as context to the large language model for processing.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="can-you-save-frequently-used-prompts-for-one-click-access">Can you save frequently used prompts for one-click access?<a href="#can-you-save-frequently-used-prompts-for-one-click-access" class="hash-link" aria-label="Direct link to Can you save frequently used prompts for one-click access?" title="Direct link to Can you save frequently used prompts for one-click access?">​</a></h3>
<p>Absolutely! AIFlow allows you to develop your own prompts as custom AI assistant menu items, eliminating the need to type the same prompts repeatedly.</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="how-to-create-custom-ai-applications">How to create custom AI applications:<a href="#how-to-create-custom-ai-applications" class="hash-link" aria-label="Direct link to How to create custom AI applications:" title="Direct link to How to create custom AI applications:">​</a></h4>
<ol>
<li>
<p><strong>Pin existing prompts</strong>: When hovering over AI-generated nodes, several function buttons appear in the upper right corner (edit title, minimize node, delete node, etc.). For certain node types, you&#x27;ll see a &quot;Pin&quot; icon button. Clicking this button saves the prompt that generated this node as a custom AI application, which will then appear in your AI Assistant menu for one-click access in the future.</p>
</li>
<li>
<p><strong>Create from scratch</strong>: In the AIFlow application interface, navigate to the Settings page. Under &quot;AI Apps/Prompts,&quot; you can easily create a new AI App that will also appear in your AI Assistant menu once saved.</p>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="cross-platform-compatibility-develop-once-use-everywhere">Cross-Platform Compatibility: Develop Once, Use Everywhere<a href="#cross-platform-compatibility-develop-once-use-everywhere" class="hash-link" aria-label="Direct link to Cross-Platform Compatibility: Develop Once, Use Everywhere" title="Direct link to Cross-Platform Compatibility: Develop Once, Use Everywhere">​</a></h3>
<p><strong>A major advantage</strong> of developing custom AI Apps and Prompts in the FunBlocks ecosystem is their cross-platform compatibility. Any AI App or Prompt you create can be used across all FunBlocks products, including:</p>
<ul>
<li>AIFlow</li>
<li>AI Docs</li>
<li>AI Extension</li>
</ul>
<p>This &quot;develop once, use everywhere&quot; approach maximizes your efficiency by ensuring that your custom tools are available no matter which FunBlocks product you&#x27;re working with. Your carefully crafted prompts and applications become a personal productivity toolkit that follows you throughout the FunBlocks platform.</p>
<p>By creating your own custom AI applications, you can significantly streamline your workflow and maximize productivity across the entire FunBlocks ecosystem, tailoring the AI assistant to your specific needs and frequent tasks.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/aiflow-tricks-and-tips/Infographics-generator"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Infographics Generator</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Multi-LLM Support</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#building-your-own-ai-apps" class="table-of-contents__link toc-highlight">Building Your Own AI Apps</a><ul><li><a href="#what-if-the-built-in-ai-features-dont-meet-your-needs" class="table-of-contents__link toc-highlight">What if the built-in AI features don&#39;t meet your needs?</a></li><li><a href="#can-you-save-frequently-used-prompts-for-one-click-access" class="table-of-contents__link toc-highlight">Can you save frequently used prompts for one-click access?</a></li><li><a href="#cross-platform-compatibility-develop-once-use-everywhere" class="table-of-contents__link toc-highlight">Cross-Platform Compatibility: Develop Once, Use Everywhere</a></li></ul></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>